{"openapi": "3.0.0", "info": {"title": "Ayafeed API", "version": "*******"}, "components": {"schemas": {"ErrorCodes": {"type": "integer", "enum": [10001, 10002, 10003, 20001, 20002, 30001, 30002, 40001, 40002, 40003, 50001, 50002, 60001, 60002, 60003], "description": "业务错误码", "x-enum-varnames": ["PARAM_MISSING_OR_INVALID", "RESOURCE_NOT_FOUND", "UNIQUE_CONSTRAINT_VIOLATION", "UNAUTHORIZED", "FORBIDDEN", "FIELD_EMPTY", "UNKNOWN_FIELD", "USER_REGISTER_FAILED", "USER_NOT_FOUND", "PASSWORD_INCORRECT", "APPEARANCE_NOT_FOUND", "APPEARANCE_BOOTH_REQUIRED", "CIRCLE_NOT_FOUND", "ARTIST_NOT_FOUND", "EVENT_NOT_FOUND"], "x-enum-descriptions": ["参数缺失或无效", "资源不存在", "唯一键冲突", "未登录", "权限不足", "fields 参数为空", "请求了未知字段", "用户注册失败", "用户不存在", "密码错误", "参展记录不存在", "booth_id 必填", "社团不存在", "作者不存在", "展会不存在"]}, "ErrorResponse": {"type": "object", "properties": {"code": {"$ref": "#/components/schemas/ErrorCodes"}, "message": {"type": "string"}, "detail": {"nullable": true}, "requestId": {"type": "string"}}, "required": ["code", "message", "requestId"]}, "PaginatedResult": {"type": "object", "properties": {"total": {"type": "integer", "example": 120}, "page": {"type": "integer", "example": 1}, "pageSize": {"type": "integer", "example": 20}, "items": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string", "example": "uuid-123"}, "name_en": {"type": "string", "example": "Reitaisai 22"}, "name_ja": {"type": "string", "example": "第二十二回博麗神社例大祭"}, "name_zh": {"type": "string", "example": "第二十二回博丽神社例大祭"}, "date_en": {"type": "string", "example": "May 3, 2025 (Sat) 10:30 – 15:30"}, "date_ja": {"type": "string", "example": "2025年5月3日(土・祝) 10:30 – 15:30"}, "date_zh": {"type": "string", "example": "2025年5月3日(周六) 10:30 – 15:30"}, "date_sort": {"type": "number", "example": 20250503}, "image_url": {"type": "string", "nullable": true}, "venue_id": {"type": "string", "example": "tokyo-big-sight"}, "url": {"type": "string", "nullable": true}, "created_at": {"type": "string"}, "updated_at": {"type": "string"}}, "required": ["id", "name_en", "name_ja", "name_zh", "date_en", "date_ja", "date_zh", "venue_id"]}}}, "required": ["total", "page", "pageSize", "items"]}, "SuccessResponse": {"type": "object", "properties": {"code": {"type": "number", "enum": [0]}, "message": {"type": "string"}, "data": {"nullable": true}}, "required": ["code", "message"]}}, "parameters": {}, "securitySchemes": {"bearerAuth": {"type": "http", "scheme": "bearer", "bearerFormat": "JWT"}, "cookieAuth": {"type": "<PERSON><PERSON><PERSON><PERSON>", "in": "cookie", "name": "refresh_token"}}}, "paths": {"/auth/sign-up/email": {"post": {"summary": "Register a new user with email", "tags": ["Better Auth"], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"email": {"type": "string", "format": "email", "description": "User email address", "example": "<EMAIL>"}, "password": {"type": "string", "minLength": 8, "description": "User password (minimum 8 characters)", "example": "password123"}, "name": {"type": "string", "description": "User display name", "example": "<PERSON>"}, "username": {"type": "string", "description": "Unique username (3-30 characters)", "example": "alice_user"}}, "required": ["email", "password"]}}}}, "responses": {"201": {"description": "User registered successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"user": {"type": "object", "properties": {"id": {"type": "string", "example": "uuid-123"}, "email": {"type": "string", "format": "email", "example": "<EMAIL>"}, "name": {"type": "string", "example": "<PERSON>"}, "username": {"type": "string", "example": "alice_user"}, "role": {"type": "string", "enum": ["admin", "editor", "viewer", "user"], "example": "user"}, "emailVerified": {"type": "boolean", "example": false}, "createdAt": {"type": "string", "format": "date-time", "example": "2024-01-01T00:00:00.000Z"}, "updatedAt": {"type": "string", "format": "date-time", "example": "2024-01-01T00:00:00.000Z"}}, "required": ["id", "email", "name", "role", "emailVerified", "createdAt", "updatedAt"]}, "session": {"type": "object", "properties": {"id": {"type": "string", "example": "session-123"}, "userId": {"type": "string", "example": "uuid-123"}, "expiresAt": {"type": "string", "format": "date-time", "example": "2024-02-01T00:00:00.000Z"}, "ipAddress": {"type": "string", "example": "***********"}, "userAgent": {"type": "string", "example": "Mozilla/5.0..."}}, "required": ["id", "userId", "expiresAt"]}}, "required": ["user", "session"]}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "409": {"description": "Email or username already exists", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string", "example": "Email already exists"}}, "required": ["error"]}}}}}, "operationId": "post_auth_signup_email"}}, "/auth/sign-in/email": {"post": {"summary": "Sign in with email and password", "tags": ["Better Auth"], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"email": {"type": "string", "format": "email", "description": "User email address", "example": "<EMAIL>"}, "password": {"type": "string", "description": "User password", "example": "password123"}}, "required": ["email", "password"]}}}}, "responses": {"200": {"description": "Login successful", "content": {"application/json": {"schema": {"type": "object", "properties": {"user": {"type": "object", "properties": {"id": {"type": "string", "example": "uuid-123"}, "email": {"type": "string", "format": "email", "example": "<EMAIL>"}, "name": {"type": "string", "example": "<PERSON>"}, "username": {"type": "string", "example": "alice_user"}, "role": {"type": "string", "enum": ["admin", "editor", "viewer", "user"], "example": "user"}, "emailVerified": {"type": "boolean", "example": false}, "createdAt": {"type": "string", "format": "date-time", "example": "2024-01-01T00:00:00.000Z"}, "updatedAt": {"type": "string", "format": "date-time", "example": "2024-01-01T00:00:00.000Z"}}, "required": ["id", "email", "name", "role", "emailVerified", "createdAt", "updatedAt"]}, "session": {"type": "object", "properties": {"id": {"type": "string", "example": "session-123"}, "userId": {"type": "string", "example": "uuid-123"}, "expiresAt": {"type": "string", "format": "date-time", "example": "2024-02-01T00:00:00.000Z"}, "ipAddress": {"type": "string", "example": "***********"}, "userAgent": {"type": "string", "example": "Mozilla/5.0..."}}, "required": ["id", "userId", "expiresAt"]}}, "required": ["user", "session"]}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "401": {"description": "Invalid credentials", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string", "example": "Invalid email or password"}}, "required": ["error"]}}}}}, "operationId": "post_auth_signin_email"}}, "/auth/sign-in/username": {"post": {"summary": "Sign in with username and password", "tags": ["Better Auth"], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"username": {"type": "string", "description": "Username", "example": "alice_user"}, "password": {"type": "string", "description": "User password", "example": "password123"}}, "required": ["username", "password"]}}}}, "responses": {"200": {"description": "Login successful", "content": {"application/json": {"schema": {"type": "object", "properties": {"user": {"type": "object", "properties": {"id": {"type": "string", "example": "uuid-123"}, "email": {"type": "string", "format": "email", "example": "<EMAIL>"}, "name": {"type": "string", "example": "<PERSON>"}, "username": {"type": "string", "example": "alice_user"}, "role": {"type": "string", "enum": ["admin", "editor", "viewer", "user"], "example": "user"}, "emailVerified": {"type": "boolean", "example": false}, "createdAt": {"type": "string", "format": "date-time", "example": "2024-01-01T00:00:00.000Z"}, "updatedAt": {"type": "string", "format": "date-time", "example": "2024-01-01T00:00:00.000Z"}}, "required": ["id", "email", "name", "role", "emailVerified", "createdAt", "updatedAt"]}, "session": {"type": "object", "properties": {"id": {"type": "string", "example": "session-123"}, "userId": {"type": "string", "example": "uuid-123"}, "expiresAt": {"type": "string", "format": "date-time", "example": "2024-02-01T00:00:00.000Z"}, "ipAddress": {"type": "string", "example": "***********"}, "userAgent": {"type": "string", "example": "Mozilla/5.0..."}}, "required": ["id", "userId", "expiresAt"]}}, "required": ["user", "session"]}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "401": {"description": "Invalid credentials", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string", "example": "Invalid username or password"}}, "required": ["error"]}}}}}, "operationId": "post_auth_signin_username"}}, "/auth/sign-out": {"post": {"summary": "Sign out current user", "tags": ["Better Auth"], "responses": {"200": {"description": "Logout successful", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}}, "required": ["success"]}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}, "operationId": "post_auth_signout"}}, "/auth/get-session": {"get": {"summary": "Get current user session", "tags": ["Better Auth"], "responses": {"200": {"description": "Session retrieved successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"user": {"type": "object", "properties": {"id": {"type": "string", "example": "uuid-123"}, "email": {"type": "string", "format": "email", "example": "<EMAIL>"}, "name": {"type": "string", "example": "<PERSON>"}, "username": {"type": "string", "example": "alice_user"}, "role": {"type": "string", "enum": ["admin", "editor", "viewer", "user"], "example": "user"}, "emailVerified": {"type": "boolean", "example": false}, "createdAt": {"type": "string", "format": "date-time", "example": "2024-01-01T00:00:00.000Z"}, "updatedAt": {"type": "string", "format": "date-time", "example": "2024-01-01T00:00:00.000Z"}}, "required": ["id", "email", "name", "role", "emailVerified", "createdAt", "updatedAt"]}, "session": {"type": "object", "properties": {"id": {"type": "string", "example": "session-123"}, "userId": {"type": "string", "example": "uuid-123"}, "expiresAt": {"type": "string", "format": "date-time", "example": "2024-02-01T00:00:00.000Z"}, "ipAddress": {"type": "string", "example": "***********"}, "userAgent": {"type": "string", "example": "Mozilla/5.0..."}}, "required": ["id", "userId", "expiresAt"]}}, "required": ["user", "session"]}}}}, "401": {"description": "Not authenticated", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string", "example": "Not authenticated"}}, "required": ["error"]}}}}}, "operationId": "get_auth_getsession"}}, "/events": {"get": {"summary": "展会列表（公开）", "tags": ["Events"], "parameters": [{"schema": {"type": "string", "example": "1"}, "required": false, "name": "page", "in": "query"}, {"schema": {"type": "string", "example": "50"}, "required": false, "name": "pageSize", "in": "query"}, {"schema": {"type": "string", "example": "<PERSON><PERSON><PERSON><PERSON>"}, "required": false, "name": "keyword", "in": "query"}, {"schema": {"type": "string", "example": "20250101"}, "required": false, "name": "date_from", "in": "query"}, {"schema": {"type": "string", "example": "20251231"}, "required": false, "name": "date_to", "in": "query"}], "responses": {"200": {"description": "展会列表", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PaginatedResult"}}}}}, "operationId": "get_events"}}, "/events/{id}": {"get": {"summary": "展会详情", "tags": ["Events"], "parameters": [{"schema": {"type": "string", "example": "uuid-123"}, "required": true, "name": "id", "in": "path"}], "responses": {"200": {"description": "展会详情", "content": {"application/json": {"schema": {"type": "object", "properties": {"id": {"type": "string", "example": "uuid-123"}, "name_en": {"type": "string", "example": "Reitaisai 22"}, "name_ja": {"type": "string", "example": "第二十二回博麗神社例大祭"}, "name_zh": {"type": "string", "example": "第二十二回博丽神社例大祭"}, "date_en": {"type": "string", "example": "May 3, 2025 (Sat) 10:30 – 15:30"}, "date_ja": {"type": "string", "example": "2025年5月3日(土・祝) 10:30 – 15:30"}, "date_zh": {"type": "string", "example": "2025年5月3日(周六) 10:30 – 15:30"}, "date_sort": {"type": "number", "example": 20250503}, "image_url": {"type": "string", "nullable": true}, "venue_id": {"type": "string", "example": "tokyo-big-sight"}, "url": {"type": "string", "nullable": true}, "created_at": {"type": "string"}, "updated_at": {"type": "string"}}, "required": ["id", "name_en", "name_ja", "name_zh", "date_en", "date_ja", "date_zh", "venue_id"]}}}}, "404": {"description": "Not Found"}}, "operationId": "get_events_id"}}, "/events/{id}/circles": {"get": {"summary": "展会社团列表", "tags": ["Events"], "parameters": [{"schema": {"type": "string"}, "required": true, "name": "id", "in": "path"}], "responses": {"200": {"description": "社团列表", "content": {"application/json": {"schema": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string", "example": "uuid-123"}, "name_en": {"type": "string", "example": "Reitaisai 22"}, "name_ja": {"type": "string", "example": "第二十二回博麗神社例大祭"}, "name_zh": {"type": "string", "example": "第二十二回博丽神社例大祭"}, "date_en": {"type": "string", "example": "May 3, 2025 (Sat) 10:30 – 15:30"}, "date_ja": {"type": "string", "example": "2025年5月3日(土・祝) 10:30 – 15:30"}, "date_zh": {"type": "string", "example": "2025年5月3日(周六) 10:30 – 15:30"}, "date_sort": {"type": "number", "example": 20250503}, "image_url": {"type": "string", "nullable": true}, "venue_id": {"type": "string", "example": "tokyo-big-sight"}, "url": {"type": "string", "nullable": true}, "created_at": {"type": "string"}, "updated_at": {"type": "string"}, "booth_id": {"type": "string", "nullable": true, "example": "あ01a"}}, "required": ["id", "name_en", "name_ja", "name_zh", "date_en", "date_ja", "date_zh", "venue_id"]}}}}}}, "operationId": "get_events_id_circles"}}, "/events/{id}/appearances": {"get": {"summary": "展会参展记录", "tags": ["Events"], "parameters": [{"schema": {"type": "string"}, "required": true, "name": "id", "in": "path"}], "responses": {"200": {"description": "参展记录分页", "content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/PaginatedResult"}, {"properties": {"total": {"type": "integer", "example": 120}, "page": {"type": "integer", "example": 1}, "pageSize": {"type": "integer", "example": 20}, "items": {"type": "array", "items": {"nullable": true}}}}]}}}}}, "operationId": "get_events_id_appearances"}}, "/venues": {"get": {"summary": "场馆列表（公开）", "tags": ["Venues"], "parameters": [{"schema": {"type": "string", "example": "1"}, "required": false, "name": "page", "in": "query"}, {"schema": {"type": "string", "example": "50"}, "required": false, "name": "pageSize", "in": "query"}, {"schema": {"type": "string", "example": "Big Sight"}, "required": false, "name": "keyword", "in": "query"}, {"schema": {"type": "string", "example": "Tokyo"}, "required": false, "name": "city", "in": "query"}, {"schema": {"type": "string", "example": "1000"}, "required": false, "name": "capacity_min", "in": "query"}, {"schema": {"type": "string", "example": "10000"}, "required": false, "name": "capacity_max", "in": "query"}, {"schema": {"type": "string", "example": "true"}, "required": false, "name": "has_parking", "in": "query"}, {"schema": {"type": "string", "example": "true"}, "required": false, "name": "has_wifi", "in": "query"}], "responses": {"200": {"description": "场馆列表", "content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/PaginatedResult"}, {"properties": {"total": {"type": "integer", "example": 120}, "page": {"type": "integer", "example": 1}, "pageSize": {"type": "integer", "example": 20}, "items": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string", "example": "tokyo-big-sight"}, "name": {"type": "string", "example": "Tokyo Big Sight"}, "address": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "lat": {"type": "number", "example": 35.6298}, "lng": {"type": "number", "example": 139.793}, "capacity": {"type": "number", "nullable": true}, "website_url": {"type": "string", "nullable": true}, "phone": {"type": "string", "nullable": true}, "facilities": {"type": "string", "nullable": true}, "transportation": {"type": "string", "nullable": true}, "parking_info": {"type": "string", "nullable": true}, "created_at": {"type": "string"}, "updated_at": {"type": "string"}}, "required": ["id", "name", "lat", "lng"]}}}}]}}}}}, "operationId": "get_venues"}}, "/venues/{id}": {"get": {"summary": "场馆详情", "tags": ["Venues"], "parameters": [{"schema": {"type": "string", "example": "tokyo-big-sight"}, "required": true, "name": "id", "in": "path"}], "responses": {"200": {"description": "场馆详情", "content": {"application/json": {"schema": {"type": "object", "properties": {"id": {"type": "string", "example": "tokyo-big-sight"}, "name": {"type": "string", "example": "Tokyo Big Sight"}, "address": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "lat": {"type": "number", "example": 35.6298}, "lng": {"type": "number", "example": 139.793}, "capacity": {"type": "number", "nullable": true}, "website_url": {"type": "string", "nullable": true}, "phone": {"type": "string", "nullable": true}, "facilities": {"type": "string", "nullable": true}, "transportation": {"type": "string", "nullable": true}, "parking_info": {"type": "string", "nullable": true}, "created_at": {"type": "string"}, "updated_at": {"type": "string"}}, "required": ["id", "name", "lat", "lng"]}}}}, "404": {"description": "Not Found"}}, "operationId": "get_venues_id"}}, "/circles": {"get": {"summary": "社团列表（公开）", "tags": ["Circles"], "parameters": [{"schema": {"type": "string", "example": "1"}, "required": false, "name": "page", "in": "query"}, {"schema": {"type": "string", "example": "50"}, "required": false, "name": "pageSize", "in": "query"}, {"schema": {"type": "string", "description": "搜索关键词，支持社团名称和作者搜索", "example": "東方"}, "required": false, "name": "search", "in": "query"}], "responses": {"200": {"description": "社团列表", "content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/PaginatedResult"}, {"properties": {"total": {"type": "integer", "example": 120}, "page": {"type": "integer", "example": 1}, "pageSize": {"type": "integer", "example": 20}, "items": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string", "example": "uuid-123"}, "name": {"type": "string", "minLength": 0, "example": "東方愛好会"}, "urls": {"type": "string", "nullable": true, "example": "{\"author\":\"<PERSON>\",\"twitter_url\":\"https://twitter.com/example\"}"}, "created_at": {"type": "string", "example": "2024-01-01T00:00:00Z"}, "updated_at": {"type": "string", "example": "2024-01-01T00:00:00Z"}}, "required": ["id", "name"]}}}}]}}}}}, "operationId": "get_circles"}}, "/circles/{id}": {"get": {"summary": "社团详情（按 ID）", "tags": ["Circles"], "parameters": [{"schema": {"type": "string", "example": "uuid-123"}, "required": true, "name": "id", "in": "path"}], "responses": {"200": {"description": "社团详情", "content": {"application/json": {"schema": {"type": "object", "properties": {"id": {"type": "string", "example": "uuid-123"}, "name": {"type": "string", "minLength": 0, "example": "東方愛好会"}, "urls": {"type": "string", "nullable": true, "example": "{\"author\":\"<PERSON>\",\"twitter_url\":\"https://twitter.com/example\"}"}, "created_at": {"type": "string", "example": "2024-01-01T00:00:00Z"}, "updated_at": {"type": "string", "example": "2024-01-01T00:00:00Z"}}, "required": ["id", "name"]}}}}, "404": {"description": "Not Found"}}, "operationId": "get_circles_id"}}, "/circles/{id}/appearances": {"get": {"summary": "社团参展历史", "tags": ["Circles"], "parameters": [{"schema": {"type": "string"}, "required": true, "name": "id", "in": "path"}, {"schema": {"type": "string", "example": "1"}, "required": false, "name": "page", "in": "query"}, {"schema": {"type": "string", "example": "50"}, "required": false, "name": "pageSize", "in": "query"}], "responses": {"200": {"description": "参展记录分页", "content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/PaginatedResult"}, {"properties": {"total": {"type": "integer", "example": 120}, "page": {"type": "integer", "example": 1}, "pageSize": {"type": "integer", "example": 20}, "items": {"type": "array", "items": {"nullable": true}}}}]}}}}}, "operationId": "get_circles_id_appearances"}}, "/circles/{circleId}/bookmark": {"post": {"summary": "切换收藏状态", "tags": ["Bookmarks"], "parameters": [{"schema": {"type": "string", "example": "circle-uuid"}, "required": true, "name": "circleId", "in": "path"}], "responses": {"200": {"description": "收藏状态已切换", "content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/SuccessResponse"}, {"type": "object", "properties": {"data": {"type": "object", "properties": {"isBookmarked": {"type": "boolean"}}, "required": ["isBookmarked"]}}, "required": ["data"]}]}}}}, "401": {"description": "未登录", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}, "operationId": "post_circles_circleId_bookmark"}}, "/circles/{circleId}/bookmark/status": {"get": {"summary": "检查收藏状态", "tags": ["Bookmarks"], "parameters": [{"schema": {"type": "string", "example": "circle-uuid"}, "required": true, "name": "circleId", "in": "path"}], "responses": {"200": {"description": "获取收藏状态成功", "content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/SuccessResponse"}, {"type": "object", "properties": {"data": {"type": "object", "properties": {"isBookmarked": {"type": "boolean", "example": true}, "bookmarkId": {"type": "string", "nullable": true, "example": "bookmark-uuid"}, "createdAt": {"type": "string", "nullable": true, "example": "2025-01-01T00:00:00Z"}}, "required": ["isBookmarked", "bookmarkId", "createdAt"]}}, "required": ["data"]}]}}}}, "401": {"description": "未登录", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}, "operationId": "get_circles_circleId_bookmark_status"}}, "/user/bookmarks": {"get": {"summary": "获取用户收藏列表", "tags": ["Bookmarks"], "parameters": [{"schema": {"type": "number", "minimum": 1, "default": 1, "example": 1}, "required": false, "name": "page", "in": "query"}, {"schema": {"type": "number", "minimum": 1, "maximum": 100, "default": 20, "example": 20}, "required": false, "name": "pageSize", "in": "query"}, {"schema": {"type": "string", "description": "Base64编码的游标，用于游标分页。提供时将忽略page参数。", "example": "eyJjcmVhdGVkX2F0IjoiMjAyNS0wMS0wMVQwMDowMDowMFoifQ=="}, "required": false, "name": "cursor", "in": "query"}, {"schema": {"type": "string", "example": "工作室"}, "required": false, "name": "search", "in": "query"}, {"schema": {"type": "string", "enum": ["created_at", "circle_name"], "default": "created_at", "example": "created_at"}, "required": false, "name": "sortBy", "in": "query"}, {"schema": {"type": "string", "enum": ["asc", "desc"], "default": "desc", "example": "desc"}, "required": false, "name": "sortOrder", "in": "query"}], "responses": {"200": {"description": "获取收藏列表成功", "content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/SuccessResponse"}, {"type": "object", "properties": {"data": {"type": "object", "properties": {"items": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string", "example": "bookmark-uuid"}, "created_at": {"type": "string", "example": "2025-01-01T00:00:00Z"}, "circle": {"type": "object", "properties": {"id": {"type": "string", "example": "circle-uuid"}, "name": {"type": "string", "example": "某某工作室"}, "category": {"type": "string", "example": "original"}, "urls": {"type": "string", "nullable": true, "example": "{\"twitter\":\"@example\"}"}, "created_at": {"type": "string", "example": "2025-01-01T00:00:00Z"}, "updated_at": {"type": "string", "example": "2025-01-01T00:00:00Z"}}, "required": ["id", "name", "category", "urls", "created_at", "updated_at"]}}, "required": ["id", "created_at", "circle"]}}, "total": {"type": "number", "example": 15}, "page": {"type": "number", "example": 1}, "pageSize": {"type": "number", "example": 20}, "totalPages": {"type": "number", "example": 1}, "nextCursor": {"type": "string", "nullable": true, "description": "下一页的游标，为null表示没有更多数据", "example": "eyJjcmVhdGVkX2F0IjoiMjAyNS0wMS0wMVQwMDowMDowMFoifQ=="}, "hasMore": {"type": "boolean", "description": "是否还有更多数据", "example": true}}, "required": ["items", "total", "page", "pageSize", "totalPages", "nextCursor", "hasMore"]}}, "required": ["data"]}]}}}}, "401": {"description": "未登录", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}, "operationId": "get_user_bookmarks"}}, "/user/bookmarks/stats": {"get": {"summary": "获取收藏统计", "description": "获取用户收藏统计信息，可选择包含收藏的社团ID列表用于前端批量状态检查优化", "tags": ["Bookmarks"], "parameters": [{"schema": {"type": "string", "description": "是否包含收藏的社团ID列表，用于前端批量状态检查优化", "example": "true"}, "required": false, "name": "includeIds", "in": "query"}], "responses": {"200": {"description": "获取收藏统计成功", "content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/SuccessResponse"}, {"type": "object", "properties": {"data": {"type": "object", "properties": {"totalBookmarks": {"type": "number", "example": 15}, "recentBookmarks": {"type": "number", "example": 3}, "categoryCounts": {"type": "object", "additionalProperties": {"type": "number"}, "example": {"original": 8, "derivative": 7}}, "bookmarkedCircleIds": {"type": "array", "items": {"type": "string"}, "description": "用户收藏的所有社团ID列表，仅在 includeIds=true 时返回，用于前端快速收藏状态检查", "example": ["circle-1", "circle-2", "circle-3"]}}, "required": ["totalBookmarks", "recentBookmarks", "categoryCounts"]}}, "required": ["data"]}]}}}}, "401": {"description": "未登录", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}, "operationId": "get_user_bookmarks_stats"}}, "/user/bookmarks/batch": {"post": {"summary": "批量操作收藏", "tags": ["Bookmarks"], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"action": {"type": "string", "enum": ["add", "remove"], "example": "add"}, "circleIds": {"type": "array", "items": {"type": "string"}, "minItems": 1, "maxItems": 50, "example": ["circle-1", "circle-2", "circle-3"]}}, "required": ["action", "circleIds"]}}}}, "responses": {"200": {"description": "批量操作成功", "content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/SuccessResponse"}, {"type": "object", "properties": {"data": {"type": "object", "properties": {"success": {"type": "array", "items": {"type": "string"}, "example": ["circle-1", "circle-2"]}, "failed": {"type": "array", "items": {"type": "object", "properties": {"circleId": {"type": "string", "example": "circle-3"}, "reason": {"type": "string", "example": "社团不存在"}}, "required": ["circleId", "reason"]}, "example": [{"circleId": "circle-3", "reason": "社团不存在"}]}, "total": {"type": "number", "example": 3}, "successCount": {"type": "number", "example": 2}, "failedCount": {"type": "number", "example": 1}}, "required": ["success", "failed", "total", "successCount", "failedCount"]}}, "required": ["data"]}]}}}}, "400": {"description": "请求参数错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "401": {"description": "未登录", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}, "operationId": "post_user_bookmarks_batch"}}, "/artists": {"get": {"summary": "作者列表", "tags": ["Artists"], "parameters": [{"schema": {"type": "string", "example": "1"}, "required": false, "name": "page", "in": "query"}, {"schema": {"type": "string", "example": "50"}, "required": false, "name": "pageSize", "in": "query"}], "responses": {"200": {"description": "作者列表", "content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/PaginatedResult"}, {"properties": {"total": {"type": "integer", "example": 120}, "page": {"type": "integer", "example": 1}, "pageSize": {"type": "integer", "example": 20}, "items": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string", "example": "uuid-123"}, "name": {"type": "string", "example": "<PERSON>"}, "urls": {"type": "string", "nullable": true}, "created_at": {"type": "string", "example": "2024-01-01T00:00:00Z"}, "updated_at": {"type": "string", "example": "2024-01-01T00:00:00Z"}, "description": {"type": "string", "nullable": true}}, "required": ["id", "name", "created_at", "updated_at"]}}}}]}}}}}, "operationId": "get_artists"}}, "/artists/{id}": {"get": {"summary": "作者详情", "tags": ["Artists"], "parameters": [{"schema": {"type": "string"}, "required": true, "name": "id", "in": "path"}, {"schema": {"type": "string"}, "required": false, "name": "lang", "in": "query"}], "responses": {"200": {"description": "作者详情", "content": {"application/json": {"schema": {"type": "object", "properties": {"id": {"type": "string", "example": "uuid-123"}, "name": {"type": "string", "example": "<PERSON>"}, "urls": {"type": "string", "nullable": true}, "created_at": {"type": "string", "example": "2024-01-01T00:00:00Z"}, "updated_at": {"type": "string", "example": "2024-01-01T00:00:00Z"}, "description": {"type": "string", "nullable": true}}, "required": ["id", "name", "created_at", "updated_at"]}}}}, "404": {"description": "Not Found"}}, "operationId": "get_artists_id"}}, "/appearances": {"get": {"summary": "参展记录查询", "tags": ["Appearances"], "deprecated": true, "parameters": [{"schema": {"type": "string"}, "required": false, "name": "circle_id", "in": "query"}, {"schema": {"type": "string"}, "required": false, "name": "event_id", "in": "query"}, {"schema": {"type": "string", "example": "1"}, "required": false, "name": "page", "in": "query"}, {"schema": {"type": "string", "example": "50"}, "required": false, "name": "pageSize", "in": "query"}], "responses": {"200": {"description": "参展记录", "content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/PaginatedResult"}, {"properties": {"total": {"type": "integer", "example": 120}, "page": {"type": "integer", "example": 1}, "pageSize": {"type": "integer", "example": 20}, "items": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string", "example": "uuid-123"}, "circle_id": {"type": "string", "example": "circle-uuid"}, "event_id": {"type": "string", "example": "event-uuid"}, "artist_id": {"type": "string", "nullable": true, "example": "artist-<PERSON><PERSON>"}, "booth_id": {"type": "string", "example": "A01a"}, "path": {"type": "string", "nullable": true, "example": "/2025/05/03/A1.jpg"}, "created_at": {"type": "string", "example": "2024-01-01T00:00:00Z"}, "updated_at": {"type": "string", "example": "2024-01-01T00:00:00Z"}}, "required": ["id", "circle_id", "event_id", "booth_id", "created_at", "updated_at"]}}}}]}}}}}, "operationId": "get_appearances"}, "post": {"summary": "创建参展记录", "tags": ["Appearances"], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"id": {"type": "string", "example": "uuid-123"}, "circle_id": {"type": "string", "example": "circle-uuid"}, "event_id": {"type": "string", "example": "event-uuid"}, "artist_id": {"type": "string", "nullable": true, "example": "artist-<PERSON><PERSON>"}, "booth_id": {"type": "string", "example": "A01a"}, "path": {"type": "string", "nullable": true, "example": "/2025/05/03/A1.jpg"}}}}}}, "responses": {"200": {"description": "success", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean"}}, "required": ["success"]}}}}}, "operationId": "post_appearances"}}, "/appearances/{id}": {"get": {"summary": "参展记录详情", "tags": ["Appearances"], "parameters": [{"schema": {"type": "string"}, "required": true, "name": "id", "in": "path"}], "responses": {"200": {"description": "详情", "content": {"application/json": {"schema": {"type": "object", "properties": {"id": {"type": "string", "example": "uuid-123"}, "circle_id": {"type": "string", "example": "circle-uuid"}, "event_id": {"type": "string", "example": "event-uuid"}, "artist_id": {"type": "string", "nullable": true, "example": "artist-<PERSON><PERSON>"}, "booth_id": {"type": "string", "example": "A01a"}, "path": {"type": "string", "nullable": true, "example": "/2025/05/03/A1.jpg"}, "created_at": {"type": "string", "example": "2024-01-01T00:00:00Z"}, "updated_at": {"type": "string", "example": "2024-01-01T00:00:00Z"}}, "required": ["id", "circle_id", "event_id", "booth_id", "created_at", "updated_at"]}}}}, "404": {"description": "Not Found"}}, "operationId": "get_appearances_id"}, "delete": {"summary": "删除参展记录", "tags": ["Appearances"], "parameters": [{"schema": {"type": "string"}, "required": true, "name": "id", "in": "path"}], "responses": {"200": {"description": "success", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean"}}, "required": ["success"]}}}}}, "operationId": "delete_appearances_id"}}, "/search": {"get": {"summary": "搜索内容", "tags": ["Search"], "parameters": [{"schema": {"type": "string", "description": "搜索关键词", "example": "Comiket"}, "required": true, "name": "q", "in": "query"}, {"schema": {"type": "string", "enum": ["all", "events", "circles"], "default": "all", "description": "搜索类型", "example": "events"}, "required": false, "name": "type", "in": "query"}, {"schema": {"type": "string", "description": "页码（暂未实现分页）", "example": "1"}, "required": false, "name": "page", "in": "query"}, {"schema": {"type": "string", "description": "每页数量（暂未实现分页）", "example": "20"}, "required": false, "name": "limit", "in": "query"}], "responses": {"200": {"description": "搜索结果", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "array", "items": {"type": "object", "properties": {"type": {"type": "string", "enum": ["event", "circle"], "description": "结果类型", "example": "event"}, "id": {"type": "string", "description": "资源ID", "example": "550e8400-e29b-41d4-a716-446655440000"}, "name": {"type": "string", "description": "名称", "example": "Comiket 103"}, "description": {"type": "string", "nullable": true, "description": "描述", "example": "世界最大的同人志即卖会"}, "venue_name": {"type": "string", "nullable": true, "description": "场馆名称（仅事件类型）", "example": "东京国际展示场"}, "start_date": {"type": "string", "nullable": true, "description": "开始时间（仅事件类型）", "example": "2024-12-30T10:00:00Z"}, "image_url": {"type": "string", "nullable": true, "description": "图片URL", "example": "https://example.com/comiket103.jpg"}, "rank": {"type": "number", "description": "搜索相关性评分", "example": 0.8567}}, "required": ["type", "id", "name", "description", "rank"]}}, "locale": {"type": "string", "description": "响应语言", "example": "zh"}, "timestamp": {"type": "string", "description": "响应时间戳", "example": "2024-01-15T10:30:00.000Z"}, "meta": {"type": "object", "properties": {"total": {"type": "number", "description": "总结果数", "example": 15}, "query": {"type": "string", "description": "搜索关键词", "example": "Comiket"}, "type": {"type": "string", "description": "搜索类型", "example": "events"}}, "required": ["total", "query", "type"]}}, "required": ["success", "data", "locale", "timestamp", "meta"]}}}}, "400": {"description": "请求参数错误"}}, "operationId": "get_search"}}, "/feed": {"get": {"summary": "获取 Feed 流", "tags": ["Feed"], "parameters": [{"schema": {"type": "string", "default": "1", "description": "页码", "example": "1"}, "required": false, "name": "page", "in": "query"}, {"schema": {"type": "string", "default": "20", "description": "每页数量", "example": "20"}, "required": false, "name": "limit", "in": "query"}, {"schema": {"type": "string", "enum": ["all", "events", "circles"], "default": "all", "description": "内容类型", "example": "all"}, "required": false, "name": "type", "in": "query"}], "responses": {"200": {"description": "Feed 流数据", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string", "description": "Feed项ID", "example": "feed-001"}, "type": {"type": "string", "enum": ["event", "circle"], "description": "内容类型", "example": "event"}, "content": {"type": "object", "properties": {"id": {"type": "string", "description": "资源ID", "example": "550e8400-e29b-41d4-a716-446655440000"}, "name": {"type": "string", "description": "名称", "example": "Comiket 103"}, "description": {"type": "string", "nullable": true, "description": "描述", "example": "世界最大的同人志即卖会"}, "start_date": {"type": "string", "nullable": true, "description": "开始时间（仅事件类型）", "example": "2024-12-30T10:00:00Z"}, "image_url": {"type": "string", "nullable": true, "description": "图片URL", "example": "https://example.com/comiket103.jpg"}}, "required": ["id", "name", "description"]}, "created_at": {"type": "string", "description": "创建时间", "example": "2024-01-15T08:00:00Z"}}, "required": ["id", "type", "content", "created_at"]}}, "locale": {"type": "string", "description": "响应语言", "example": "zh"}, "timestamp": {"type": "string", "description": "响应时间戳", "example": "2024-01-15T10:30:00.000Z"}, "meta": {"type": "object", "properties": {"total": {"type": "number", "description": "总数量", "example": 200}, "page": {"type": "number", "description": "当前页码", "example": 1}, "limit": {"type": "number", "description": "每页数量", "example": 20}, "hasMore": {"type": "boolean", "description": "是否有更多数据", "example": true}}, "required": ["total", "page", "limit", "hasMore"]}}, "required": ["success", "data", "locale", "timestamp", "meta"]}}}}, "400": {"description": "请求参数错误"}}, "operationId": "get_feed"}}, "/images/batch": {"get": {"summary": "批量查询图片", "tags": ["Images"], "parameters": [{"schema": {"type": "string", "description": "事件ID列表，用逗号分隔", "example": "event1,event2,event3"}, "required": true, "name": "events", "in": "query"}, {"schema": {"type": "string", "enum": ["original", "large", "medium", "thumb"], "description": "按变体筛选", "example": "medium"}, "required": false, "name": "variant", "in": "query"}, {"schema": {"type": "string", "enum": ["poster", "logo", "banner", "gallery"], "description": "按图片类型筛选", "example": "poster"}, "required": false, "name": "imageType", "in": "query"}], "responses": {"200": {"description": "批量查询结果", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "number", "example": 200}, "message": {"type": "string", "example": "批量查询成功"}, "data": {"type": "object", "additionalProperties": {"type": "object", "nullable": true, "properties": {"id": {"type": "string", "example": "uuid-123"}, "group_id": {"type": "string", "example": "group-uuid-456"}, "resource_type": {"type": "string", "enum": ["event", "circle", "venue"], "example": "event"}, "resource_id": {"type": "string", "example": "resource-uuid-789"}, "image_type": {"type": "string", "enum": ["poster", "logo", "banner", "gallery"], "example": "poster"}, "variant": {"type": "string", "enum": ["original", "large", "medium", "thumb"], "example": "thumb"}, "file_path": {"type": "string", "example": "/images/events/reitaisai-22/poster_thumb.jpg"}, "file_size": {"type": "number", "nullable": true, "example": 51200}, "width": {"type": "number", "nullable": true, "example": 200}, "height": {"type": "number", "nullable": true, "example": 300}, "format": {"type": "string", "nullable": true, "example": "jpeg"}, "created_at": {"type": "string", "example": "2025-01-30T00:00:00Z"}, "updated_at": {"type": "string", "example": "2025-01-30T00:00:00Z"}}, "required": ["id", "group_id", "resource_type", "resource_id", "image_type", "variant", "file_path"]}, "example": {"event1": {"id": "img-456", "variant": "medium", "file_path": "/images/events/event1/poster_medium.jpg"}, "event2": {"id": "img-789", "variant": "medium", "file_path": "/images/events/event2/poster_medium.jpg"}, "event3": null}}}, "required": ["code", "message", "data"]}}}}, "400": {"description": "参数错误"}}, "operationId": "get_images_batch"}}, "/images/{id}/file": {"get": {"summary": "直接访问图片文件", "tags": ["Images"], "parameters": [{"schema": {"type": "string", "description": "图片ID", "example": "uuid-123"}, "required": true, "name": "id", "in": "path"}], "responses": {"200": {"description": "图片文件", "content": {"image/*": {"schema": {"type": "string", "format": "binary"}}}}, "404": {"description": "图片不存在"}}, "operationId": "get_images_id_file"}}, "/images/{id}": {"get": {"summary": "获取单个图片信息", "tags": ["Images"], "parameters": [{"schema": {"type": "string", "description": "图片ID", "example": "uuid-123"}, "required": true, "name": "id", "in": "path"}], "responses": {"200": {"description": "图片信息", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "number", "example": 0}, "message": {"type": "string", "example": "OK"}, "data": {"type": "object", "properties": {"id": {"type": "string", "example": "uuid-123"}, "group_id": {"type": "string", "example": "group-uuid-456"}, "resource_type": {"type": "string", "enum": ["event", "circle", "venue"], "example": "event"}, "resource_id": {"type": "string", "example": "resource-uuid-789"}, "image_type": {"type": "string", "enum": ["poster", "logo", "banner", "gallery"], "example": "poster"}, "variant": {"type": "string", "enum": ["original", "large", "medium", "thumb"], "example": "thumb"}, "file_path": {"type": "string", "example": "/images/events/reitaisai-22/poster_thumb.jpg"}, "file_size": {"type": "number", "nullable": true, "example": 51200}, "width": {"type": "number", "nullable": true, "example": 200}, "height": {"type": "number", "nullable": true, "example": 300}, "format": {"type": "string", "nullable": true, "example": "jpeg"}, "created_at": {"type": "string", "example": "2025-01-30T00:00:00Z"}, "updated_at": {"type": "string", "example": "2025-01-30T00:00:00Z"}}, "required": ["id", "group_id", "resource_type", "resource_id", "image_type", "variant", "file_path"]}}, "required": ["code", "message", "data"]}}}}, "404": {"description": "图片不存在"}}, "operationId": "get_images_id"}}, "/images/{category}/{resourceId}": {"get": {"summary": "获取资源的图片列表", "tags": ["Images"], "parameters": [{"schema": {"type": "string", "enum": ["event", "circle", "venue"], "description": "资源分类", "example": "event"}, "required": true, "name": "category", "in": "path"}, {"schema": {"type": "string", "description": "资源ID", "example": "resource-uuid-789"}, "required": true, "name": "resourceId", "in": "path"}, {"schema": {"type": "string", "example": "1"}, "required": false, "name": "page", "in": "query"}, {"schema": {"type": "string", "example": "20"}, "required": false, "name": "pageSize", "in": "query"}, {"schema": {"type": "string", "enum": ["original", "large", "medium", "thumb"], "description": "按变体筛选", "example": "thumb"}, "required": false, "name": "variant", "in": "query"}, {"schema": {"type": "string", "enum": ["poster", "logo", "banner", "gallery"], "description": "按图片类型筛选", "example": "poster"}, "required": false, "name": "imageType", "in": "query"}], "responses": {"200": {"description": "图片列表", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "number", "example": 0}, "message": {"type": "string", "example": "OK"}, "data": {"type": "object", "properties": {"images": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string", "example": "uuid-123"}, "group_id": {"type": "string", "example": "group-uuid-456"}, "resource_type": {"type": "string", "enum": ["event", "circle", "venue"], "example": "event"}, "resource_id": {"type": "string", "example": "resource-uuid-789"}, "image_type": {"type": "string", "enum": ["poster", "logo", "banner", "gallery"], "example": "poster"}, "variant": {"type": "string", "enum": ["original", "large", "medium", "thumb"], "example": "thumb"}, "file_path": {"type": "string", "example": "/images/events/reitaisai-22/poster_thumb.jpg"}, "file_size": {"type": "number", "nullable": true, "example": 51200}, "width": {"type": "number", "nullable": true, "example": 200}, "height": {"type": "number", "nullable": true, "example": 300}, "format": {"type": "string", "nullable": true, "example": "jpeg"}, "created_at": {"type": "string", "example": "2025-01-30T00:00:00Z"}, "updated_at": {"type": "string", "example": "2025-01-30T00:00:00Z"}}, "required": ["id", "group_id", "resource_type", "resource_id", "image_type", "variant", "file_path"]}}, "pagination": {"type": "object", "properties": {"page": {"type": "number", "example": 1}, "pageSize": {"type": "number", "example": 20}, "total": {"type": "number", "example": 100}, "totalPages": {"type": "number", "example": 5}}, "required": ["page", "pageSize", "total", "totalPages"]}}, "required": ["images", "pagination"]}}, "required": ["code", "message", "data"]}}}}}, "operationId": "get_images_category_resourceId"}}, "/rich-text/{entityType}/{entityId}/content": {"get": {"summary": "获取实体的所有富文本内容", "description": "获取指定实体（事件、场馆、社团）的所有富文本内容", "tags": ["Rich Text"], "parameters": [{"schema": {"type": "string", "enum": ["event", "venue", "circle"], "example": "event"}, "required": true, "name": "entityType", "in": "path"}, {"schema": {"type": "string", "example": "reitaisai-22"}, "required": true, "name": "entityId", "in": "path"}], "responses": {"200": {"description": "获取成功", "content": {"application/json": {"schema": {"type": "object", "properties": {"introduction": {"type": "string"}, "highlights": {"type": "string"}, "guide": {"type": "string"}, "notices": {"type": "string"}}}}}}, "400": {"description": "请求参数错误"}, "404": {"description": "实体不存在"}}, "operationId": "get_richtext_entityType_entityId_content"}, "post": {"summary": "创建或更新单个内容", "description": "创建或更新指定实体的单个富文本内容", "tags": ["Rich Text"], "parameters": [{"schema": {"type": "string", "enum": ["event", "venue", "circle"], "example": "event"}, "required": true, "name": "entityType", "in": "path"}, {"schema": {"type": "string", "example": "reitaisai-22"}, "required": true, "name": "entityId", "in": "path"}], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"entity_type": {"type": "string", "enum": ["event", "venue", "circle"]}, "entity_id": {"type": "string", "minLength": 1}, "content_type": {"type": "string", "enum": ["introduction", "highlights", "guide", "notices"]}, "content": {"type": "string", "maxLength": 100000}}, "required": ["entity_type", "entity_id", "content_type", "content"]}}}}, "responses": {"201": {"description": "创建成功", "content": {"application/json": {"schema": {"type": "object", "properties": {"id": {"type": "string", "example": "uuid-123"}, "entity_type": {"type": "string", "enum": ["event", "venue", "circle"], "example": "event"}, "entity_id": {"type": "string", "example": "reitaisai-22"}, "content_type": {"type": "string", "enum": ["introduction", "highlights", "guide", "notices"], "example": "introduction"}, "content": {"type": "string", "example": "<p>这是一个富文本内容示例</p>"}, "created_at": {"type": "string"}, "updated_at": {"type": "string"}}, "required": ["id", "entity_type", "entity_id", "content_type", "content"]}}}}, "400": {"description": "请求参数错误"}}, "operationId": "post_richtext_entityType_entityId_content"}, "put": {"summary": "批量更新内容", "description": "批量更新指定实体的所有富文本内容", "tags": ["Rich Text"], "parameters": [{"schema": {"type": "string", "enum": ["event", "venue", "circle"], "example": "event"}, "required": true, "name": "entityType", "in": "path"}, {"schema": {"type": "string", "example": "reitaisai-22"}, "required": true, "name": "entityId", "in": "path"}], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"introduction": {"type": "string", "maxLength": 100000}, "highlights": {"type": "string", "maxLength": 100000}, "guide": {"type": "string", "maxLength": 100000}, "notices": {"type": "string", "maxLength": 100000}}}}}}, "responses": {"200": {"description": "更新成功", "content": {"application/json": {"schema": {"type": "object", "properties": {"introduction": {"type": "string"}, "highlights": {"type": "string"}, "guide": {"type": "string"}, "notices": {"type": "string"}}}}}}, "400": {"description": "请求参数错误"}}, "operationId": "put_richtext_entityType_entityId_content"}, "delete": {"summary": "删除实体的所有内容", "description": "删除指定实体的所有富文本内容", "tags": ["Rich Text"], "parameters": [{"schema": {"type": "string", "enum": ["event", "venue", "circle"], "example": "event"}, "required": true, "name": "entityType", "in": "path"}, {"schema": {"type": "string", "example": "reitaisai-22"}, "required": true, "name": "entityId", "in": "path"}], "responses": {"200": {"description": "删除成功"}, "400": {"description": "请求参数错误"}}, "operationId": "delete_richtext_entityType_entityId_content"}}, "/rich-text/{entityType}/{entityId}/content/{contentType}": {"get": {"summary": "获取实体的特定类型内容", "description": "获取指定实体的特定类型富文本内容", "tags": ["Rich Text"], "parameters": [{"schema": {"type": "string", "enum": ["event", "venue", "circle"], "example": "event"}, "required": true, "name": "entityType", "in": "path"}, {"schema": {"type": "string", "example": "reitaisai-22"}, "required": true, "name": "entityId", "in": "path"}, {"schema": {"type": "string", "enum": ["introduction", "highlights", "guide", "notices"], "example": "introduction"}, "required": true, "name": "contentType", "in": "path"}], "responses": {"200": {"description": "获取成功", "content": {"application/json": {"schema": {"type": "object", "properties": {"content": {"type": "string"}}}}}}, "400": {"description": "请求参数错误"}, "404": {"description": "内容不存在"}}, "operationId": "get_richtext_entityType_entityId_content_contentType"}}, "/rich-text/api/upload/images": {"post": {"summary": "富文本编辑器图片上传", "description": "为富文本编辑器提供图片上传功能", "tags": ["Rich Text"], "requestBody": {"content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"image": {"type": "string", "format": "binary", "description": "图片文件"}}, "required": ["image"]}}}}, "responses": {"200": {"description": "上传成功", "content": {"application/json": {"schema": {"type": "object", "properties": {"url": {"type": "string", "example": "/images/content/1640995200000_example.jpg", "description": "图片访问URL"}}}}}}, "400": {"description": "请求参数错误或文件验证失败"}, "500": {"description": "上传失败"}}, "operationId": "post_richtext_api_upload_images"}}, "/admin/events": {"get": {"summary": "展会列表（分页 & 搜索）", "tags": ["Admin.Events"], "parameters": [{"schema": {"type": "string", "example": "1"}, "required": false, "name": "page", "in": "query"}, {"schema": {"type": "string", "example": "50"}, "required": false, "name": "pageSize", "in": "query"}, {"schema": {"type": "string", "example": "例大祭"}, "required": false, "name": "keyword", "in": "query"}, {"schema": {"type": "string", "example": "20250101"}, "required": false, "name": "date_from", "in": "query"}, {"schema": {"type": "string", "example": "20251231"}, "required": false, "name": "date_to", "in": "query"}], "responses": {"200": {"description": "分页列表", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PaginatedResult"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}, "operationId": "get_admin_events"}, "post": {"summary": "创建展会", "tags": ["Admin.Events"], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"name_en": {"type": "string", "example": "Reitaisai 22"}, "name_ja": {"type": "string", "example": "第二十二回博麗神社例大祭"}, "name_zh": {"type": "string", "example": "第二十二回博丽神社例大祭"}, "date_en": {"type": "string", "example": "May 3, 2025 (Sat) 10:30 – 15:30"}, "date_ja": {"type": "string", "example": "2025年5月3日(土・祝) 10:30 – 15:30"}, "date_zh": {"type": "string", "example": "2025年5月3日(周六) 10:30 – 15:30"}, "date_sort": {"type": "number", "example": 20250503}, "image_url": {"type": "string", "nullable": true}, "venue_id": {"type": "string", "example": "tokyo-big-sight"}, "url": {"type": "string", "nullable": true}}, "required": ["name_en", "name_ja", "name_zh", "date_en", "date_ja", "date_zh", "venue_id"]}}}}, "responses": {"201": {"description": "展会创建成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SuccessResponse"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "422": {"description": "Validation Error"}}, "operationId": "post_admin_events"}}, "/admin/events/{id}": {"get": {"summary": "展会详情", "tags": ["Admin.Events"], "parameters": [{"schema": {"type": "string", "example": "uuid-123"}, "required": true, "name": "id", "in": "path"}], "responses": {"200": {"description": "展会详情", "content": {"application/json": {"schema": {"type": "object", "properties": {"id": {"type": "string", "example": "uuid-123"}, "name_en": {"type": "string", "example": "Reitaisai 22"}, "name_ja": {"type": "string", "example": "第二十二回博麗神社例大祭"}, "name_zh": {"type": "string", "example": "第二十二回博丽神社例大祭"}, "date_en": {"type": "string", "example": "May 3, 2025 (Sat) 10:30 – 15:30"}, "date_ja": {"type": "string", "example": "2025年5月3日(土・祝) 10:30 – 15:30"}, "date_zh": {"type": "string", "example": "2025年5月3日(周六) 10:30 – 15:30"}, "date_sort": {"type": "number", "example": 20250503}, "image_url": {"type": "string", "nullable": true}, "venue_id": {"type": "string", "example": "tokyo-big-sight"}, "url": {"type": "string", "nullable": true}, "created_at": {"type": "string"}, "updated_at": {"type": "string"}}, "required": ["id", "name_en", "name_ja", "name_zh", "date_en", "date_ja", "date_zh", "venue_id"]}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}, "operationId": "get_admin_events_id"}, "patch": {"summary": "更新展会", "tags": ["Admin.Events"], "parameters": [{"schema": {"type": "string", "example": "uuid-123"}, "required": true, "name": "id", "in": "path"}], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"name_en": {"type": "string", "example": "Reitaisai 22"}, "name_ja": {"type": "string", "example": "第二十二回博麗神社例大祭"}, "name_zh": {"type": "string", "example": "第二十二回博丽神社例大祭"}, "date_en": {"type": "string", "example": "May 3, 2025 (Sat) 10:30 – 15:30"}, "date_ja": {"type": "string", "example": "2025年5月3日(土・祝) 10:30 – 15:30"}, "date_zh": {"type": "string", "example": "2025年5月3日(周六) 10:30 – 15:30"}, "date_sort": {"type": "number", "example": 20250503}, "image_url": {"type": "string", "nullable": true}, "venue_id": {"type": "string", "example": "tokyo-big-sight"}, "url": {"type": "string", "nullable": true}}}}}}, "responses": {"200": {"description": "展会已保存", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SuccessResponse"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "422": {"description": "Validation Error"}}, "operationId": "patch_admin_events_id"}, "delete": {"summary": "删除展会", "tags": ["Admin.Events"], "parameters": [{"schema": {"type": "string", "example": "uuid-123"}, "required": true, "name": "id", "in": "path"}], "responses": {"200": {"description": "展会已删除", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string"}}, "required": ["message"]}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}, "operationId": "delete_admin_events_id"}}, "/admin/circles": {"get": {"summary": "社团列表", "tags": ["Admin.Circles"], "responses": {"200": {"description": "社团列表", "content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/PaginatedResult"}, {"properties": {"total": {"type": "integer", "example": 120}, "page": {"type": "integer", "example": 1}, "pageSize": {"type": "integer", "example": 20}, "items": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string", "example": "uuid-123"}, "name": {"type": "string", "minLength": 0, "example": "東方愛好会"}, "urls": {"type": "string", "nullable": true, "example": "{\"author\":\"<PERSON>\",\"twitter_url\":\"https://twitter.com/example\"}"}, "created_at": {"type": "string", "example": "2024-01-01T00:00:00Z"}, "updated_at": {"type": "string", "example": "2024-01-01T00:00:00Z"}}, "required": ["id", "name"]}}}}]}}}}}, "operationId": "get_admin_circles"}, "post": {"summary": "创建社团", "tags": ["Admin.Circles"], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"name": {"type": "string", "example": "東方愛好会"}, "author": {"type": "string", "example": "<PERSON>"}, "twitter_url": {"type": "string", "example": "https://twitter.com/example"}, "pixiv_url": {"type": "string", "example": "https://pixiv.net/users/123"}, "web_url": {"type": "string", "example": "https://example.com"}}, "required": ["name"]}}}}, "responses": {"201": {"description": "社团创建成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SuccessResponse"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "409": {"description": "Conflict", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "422": {"description": "Validation Error"}}, "operationId": "post_admin_circles"}}, "/admin/circles/{id}": {"get": {"summary": "社团详情", "tags": ["Admin.Circles"], "parameters": [{"schema": {"type": "string", "example": "uuid-123"}, "required": true, "name": "id", "in": "path"}], "responses": {"200": {"description": "社团详情", "content": {"application/json": {"schema": {"type": "object", "properties": {"id": {"type": "string", "example": "uuid-123"}, "name": {"type": "string", "minLength": 0, "example": "東方愛好会"}, "urls": {"type": "string", "nullable": true, "example": "{\"author\":\"<PERSON>\",\"twitter_url\":\"https://twitter.com/example\"}"}, "created_at": {"type": "string", "example": "2024-01-01T00:00:00Z"}, "updated_at": {"type": "string", "example": "2024-01-01T00:00:00Z"}}, "required": ["id", "name"]}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}, "operationId": "get_admin_circles_id"}, "put": {"summary": "更新社团", "tags": ["Admin.Circles"], "parameters": [{"schema": {"type": "string", "example": "uuid-123"}, "required": true, "name": "id", "in": "path"}], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"name": {"type": "string"}, "author": {"type": "string"}, "twitter_url": {"type": "string"}, "pixiv_url": {"type": "string"}, "web_url": {"type": "string"}}}}}}, "responses": {"200": {"description": "社团已保存", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SuccessResponse"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "422": {"description": "Validation Error"}}, "operationId": "put_admin_circles_id"}, "delete": {"summary": "删除社团", "tags": ["Admin.Circles"], "parameters": [{"schema": {"type": "string", "example": "uuid-123"}, "required": true, "name": "id", "in": "path"}], "responses": {"200": {"description": "社团已删除", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string"}}, "required": ["message"]}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}, "operationId": "delete_admin_circles_id"}}, "/admin/venues": {"get": {"summary": "场馆列表（管理员）", "tags": ["Admin.<PERSON>"], "parameters": [{"schema": {"type": "string", "example": "1"}, "required": false, "name": "page", "in": "query"}, {"schema": {"type": "string", "example": "50"}, "required": false, "name": "pageSize", "in": "query"}, {"schema": {"type": "string", "example": "Big Sight"}, "required": false, "name": "keyword", "in": "query"}, {"schema": {"type": "string", "example": "Tokyo"}, "required": false, "name": "city", "in": "query"}, {"schema": {"type": "string", "example": "1000"}, "required": false, "name": "capacity_min", "in": "query"}, {"schema": {"type": "string", "example": "10000"}, "required": false, "name": "capacity_max", "in": "query"}, {"schema": {"type": "string", "example": "true"}, "required": false, "name": "has_parking", "in": "query"}, {"schema": {"type": "string", "example": "true"}, "required": false, "name": "has_wifi", "in": "query"}], "responses": {"200": {"description": "场馆列表", "content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/PaginatedResult"}, {"properties": {"total": {"type": "integer", "example": 120}, "page": {"type": "integer", "example": 1}, "pageSize": {"type": "integer", "example": 20}, "items": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string", "example": "tokyo-big-sight"}, "name_en": {"type": "string", "example": "Tokyo Big Sight"}, "name_ja": {"type": "string", "example": "東京ビッグサイト"}, "name_zh": {"type": "string", "example": "东京 Big Sight"}, "address_en": {"type": "string", "nullable": true}, "address_ja": {"type": "string", "nullable": true}, "address_zh": {"type": "string", "nullable": true}, "lat": {"type": "number", "example": 35.6298}, "lng": {"type": "number", "example": 139.793}, "capacity": {"type": "number", "nullable": true}, "website_url": {"type": "string", "nullable": true}, "phone": {"type": "string", "nullable": true}, "description_en": {"type": "string", "nullable": true}, "description_ja": {"type": "string", "nullable": true}, "description_zh": {"type": "string", "nullable": true}, "facilities": {"type": "string", "nullable": true}, "transportation": {"type": "string", "nullable": true}, "parking_info": {"type": "string", "nullable": true}, "created_at": {"type": "string"}, "updated_at": {"type": "string"}}, "required": ["id", "name_en", "name_ja", "name_zh", "lat", "lng"]}}}}]}}}}}, "operationId": "get_admin_venues"}, "post": {"summary": "创建场馆", "tags": ["Admin.<PERSON>"], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"name_en": {"type": "string", "example": "Tokyo Big Sight"}, "name_ja": {"type": "string", "example": "東京ビッグサイト"}, "name_zh": {"type": "string", "example": "东京 Big Sight"}, "address_en": {"type": "string", "nullable": true}, "address_ja": {"type": "string", "nullable": true}, "address_zh": {"type": "string", "nullable": true}, "lat": {"type": "number", "example": 35.6298}, "lng": {"type": "number", "example": 139.793}, "capacity": {"type": "number", "nullable": true}, "website_url": {"type": "string", "nullable": true}, "phone": {"type": "string", "nullable": true}, "description_en": {"type": "string", "nullable": true}, "description_ja": {"type": "string", "nullable": true}, "description_zh": {"type": "string", "nullable": true}, "facilities": {"type": "string", "nullable": true}, "transportation": {"type": "string", "nullable": true}, "parking_info": {"type": "string", "nullable": true}}, "required": ["name_en", "name_ja", "name_zh", "lat", "lng"]}}}}, "responses": {"201": {"description": "创建成功", "content": {"application/json": {"schema": {"type": "object", "properties": {"id": {"type": "string", "example": "tokyo-big-sight"}, "name_en": {"type": "string", "example": "Tokyo Big Sight"}, "name_ja": {"type": "string", "example": "東京ビッグサイト"}, "name_zh": {"type": "string", "example": "东京 Big Sight"}, "address_en": {"type": "string", "nullable": true}, "address_ja": {"type": "string", "nullable": true}, "address_zh": {"type": "string", "nullable": true}, "lat": {"type": "number", "example": 35.6298}, "lng": {"type": "number", "example": 139.793}, "capacity": {"type": "number", "nullable": true}, "website_url": {"type": "string", "nullable": true}, "phone": {"type": "string", "nullable": true}, "description_en": {"type": "string", "nullable": true}, "description_ja": {"type": "string", "nullable": true}, "description_zh": {"type": "string", "nullable": true}, "facilities": {"type": "string", "nullable": true}, "transportation": {"type": "string", "nullable": true}, "parking_info": {"type": "string", "nullable": true}, "created_at": {"type": "string"}, "updated_at": {"type": "string"}}, "required": ["id", "name_en", "name_ja", "name_zh", "lat", "lng"]}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}, "operationId": "post_admin_venues"}}, "/admin/venues/{id}": {"get": {"summary": "场馆详情（管理员）", "tags": ["Admin.<PERSON>"], "parameters": [{"schema": {"type": "string"}, "required": true, "name": "id", "in": "path"}], "responses": {"200": {"description": "场馆详情", "content": {"application/json": {"schema": {"type": "object", "properties": {"id": {"type": "string", "example": "tokyo-big-sight"}, "name_en": {"type": "string", "example": "Tokyo Big Sight"}, "name_ja": {"type": "string", "example": "東京ビッグサイト"}, "name_zh": {"type": "string", "example": "东京 Big Sight"}, "address_en": {"type": "string", "nullable": true}, "address_ja": {"type": "string", "nullable": true}, "address_zh": {"type": "string", "nullable": true}, "lat": {"type": "number", "example": 35.6298}, "lng": {"type": "number", "example": 139.793}, "capacity": {"type": "number", "nullable": true}, "website_url": {"type": "string", "nullable": true}, "phone": {"type": "string", "nullable": true}, "description_en": {"type": "string", "nullable": true}, "description_ja": {"type": "string", "nullable": true}, "description_zh": {"type": "string", "nullable": true}, "facilities": {"type": "string", "nullable": true}, "transportation": {"type": "string", "nullable": true}, "parking_info": {"type": "string", "nullable": true}, "created_at": {"type": "string"}, "updated_at": {"type": "string"}}, "required": ["id", "name_en", "name_ja", "name_zh", "lat", "lng"]}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}, "operationId": "get_admin_venues_id"}, "put": {"summary": "更新场馆", "tags": ["Admin.<PERSON>"], "parameters": [{"schema": {"type": "string"}, "required": true, "name": "id", "in": "path"}], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"name_en": {"type": "string", "example": "Tokyo Big Sight"}, "name_ja": {"type": "string", "example": "東京ビッグサイト"}, "name_zh": {"type": "string", "example": "东京 Big Sight"}, "address_en": {"type": "string", "nullable": true}, "address_ja": {"type": "string", "nullable": true}, "address_zh": {"type": "string", "nullable": true}, "lat": {"type": "number", "example": 35.6298}, "lng": {"type": "number", "example": 139.793}, "capacity": {"type": "number", "nullable": true}, "website_url": {"type": "string", "nullable": true}, "phone": {"type": "string", "nullable": true}, "description_en": {"type": "string", "nullable": true}, "description_ja": {"type": "string", "nullable": true}, "description_zh": {"type": "string", "nullable": true}, "facilities": {"type": "string", "nullable": true}, "transportation": {"type": "string", "nullable": true}, "parking_info": {"type": "string", "nullable": true}}}}}}, "responses": {"200": {"description": "更新成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SuccessResponse"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}, "operationId": "put_admin_venues_id"}, "delete": {"summary": "删除场馆", "tags": ["Admin.<PERSON>"], "parameters": [{"schema": {"type": "string"}, "required": true, "name": "id", "in": "path"}], "responses": {"200": {"description": "删除成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SuccessResponse"}}}}, "400": {"description": "场馆正在使用中，无法删除", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}, "operationId": "delete_admin_venues_id"}}, "/admin/images/upload": {"post": {"summary": "上传图片", "tags": ["Admin.Images"], "requestBody": {"content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"file": {"type": "string", "description": "图片文件", "format": "binary"}, "category": {"type": "string", "enum": ["event", "circle", "venue"], "description": "图片分类", "example": "event"}, "resourceId": {"type": "string", "description": "关联的资源ID", "example": "resource-uuid-789"}, "imageType": {"type": "string", "enum": ["poster", "logo", "banner", "gallery"], "description": "图片类型", "example": "poster"}, "variant": {"type": "string", "enum": ["original", "large", "medium", "thumb"], "description": "图片变体", "example": "thumb"}, "groupId": {"type": "string", "description": "关联同一组图片的标识，可选", "example": "group-uuid-456"}}, "required": ["category", "resourceId", "imageType", "variant"]}}}}, "responses": {"201": {"description": "图片上传成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SuccessResponse"}}}}, "400": {"description": "请求参数错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}, "operationId": "post_admin_images_upload"}}, "/admin/images": {"delete": {"summary": "删除图片", "tags": ["Admin.Images"], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"relativePaths": {"type": "array", "items": {"type": "string"}, "description": "要删除的图片相对路径列表", "example": ["/images/events/reitaisai-22/poster_thumb.jpg"]}}, "required": ["relativePaths"]}}}}, "responses": {"200": {"description": "图片删除完成", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SuccessResponse"}}}}, "400": {"description": "请求参数错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}, "operationId": "delete_admin_images"}}, "/admin/images/{id}": {"get": {"summary": "获取图片详细信息", "tags": ["Admin.Images"], "parameters": [{"schema": {"type": "string", "description": "图片ID", "example": "uuid-123"}, "required": true, "name": "id", "in": "path"}], "responses": {"200": {"description": "图片信息", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SuccessResponse"}}}}, "404": {"description": "图片不存在", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}, "operationId": "get_admin_images_id"}}, "/admin/users": {"get": {"summary": "用户列表", "tags": ["Admin.Users"], "responses": {"200": {"description": "用户列表", "content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/PaginatedResult"}, {"properties": {"total": {"type": "integer", "example": 120}, "page": {"type": "integer", "example": 1}, "pageSize": {"type": "integer", "example": 20}, "items": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string", "example": "uuid-123"}, "username": {"type": "string", "example": "alice"}, "role": {"type": "string", "example": "viewer"}}, "required": ["id", "username", "role"]}}}}]}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}, "operationId": "get_admin_users"}, "post": {"summary": "创建用户", "tags": ["Admin.Users"], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"username": {"type": "string", "minLength": 1, "example": "alice"}, "password": {"type": "string", "minLength": 8, "example": "pwd12345"}, "role": {"type": "string", "example": "viewer"}}, "required": ["username", "password"]}}}}, "responses": {"201": {"description": "用户创建成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SuccessResponse"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "422": {"description": "Validation Error"}}, "operationId": "post_admin_users"}}, "/admin/users/{id}": {"get": {"summary": "用户详情", "tags": ["Admin.Users"], "parameters": [{"schema": {"type": "string", "example": "uuid-123"}, "required": true, "name": "id", "in": "path"}], "responses": {"200": {"description": "用户详情", "content": {"application/json": {"schema": {"type": "object", "properties": {"id": {"type": "string", "example": "uuid-123"}, "username": {"type": "string", "example": "alice"}, "role": {"type": "string", "example": "viewer"}}, "required": ["id", "username", "role"]}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}, "operationId": "get_admin_users_id"}, "put": {"summary": "更新用户", "tags": ["Admin.Users"], "parameters": [{"schema": {"type": "string", "example": "uuid-123"}, "required": true, "name": "id", "in": "path"}], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"username": {"type": "string", "example": "alice"}, "role": {"type": "string", "example": "editor"}, "password": {"type": "string", "example": "newpassword"}}}}}}, "responses": {"200": {"description": "用户信息已更新", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SuccessResponse"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "422": {"description": "Validation Error"}}, "operationId": "put_admin_users_id"}, "delete": {"summary": "删除用户", "tags": ["Admin.Users"], "parameters": [{"schema": {"type": "string", "example": "uuid-123"}, "required": true, "name": "id", "in": "path"}], "responses": {"200": {"description": "用户已删除", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string"}}, "required": ["message"]}}}}}, "operationId": "delete_admin_users_id"}}, "/admin/logs": {"get": {"summary": "查询操作日志", "tags": ["Admin.Logs"], "responses": {"200": {"description": "日志列表"}}, "operationId": "get_admin_logs"}}, "/admin/stats": {"get": {"summary": "后台统计数据", "tags": ["Admin.Stats"], "parameters": [{"schema": {"type": "string", "example": "2025"}, "required": false, "name": "year", "in": "query"}], "responses": {"200": {"description": "统计数据", "content": {"application/json": {"schema": {"type": "object", "properties": {"totals": {"type": "object", "properties": {"circles": {"type": "number", "example": 123}, "artists": {"type": "number", "example": 456}, "events": {"type": "number", "example": 78}}, "required": ["circles", "artists", "events"]}, "year": {"type": "number", "example": 2025}, "eventsByMonth": {"type": "array", "items": {"type": "object", "properties": {"month": {"type": "string", "example": "01"}, "count": {"type": "number", "example": 12}}, "required": ["month", "count"]}}}, "required": ["totals", "year", "eventsByMonth"]}}}}}, "operationId": "get_admin_stats"}}}, "tags": [{"name": "<PERSON><PERSON>", "description": "认证相关接口"}, {"name": "Admin.Circles", "description": "后台社团管理接口"}, {"name": "Admin.Events", "description": "后台展会管理接口"}, {"name": "Admin.Users", "description": "后台用户管理接口"}, {"name": "Admin.Images", "description": "后台图片管理接口"}, {"name": "Admin.<PERSON>", "description": "后台场馆管理接口"}, {"name": "Admin.Logs", "description": "后台日志接口"}, {"name": "Admin.Stats", "description": "后台统计接口"}, {"name": "Events", "description": "展会公开接口"}, {"name": "Venues", "description": "场馆公开接口"}, {"name": "Circles", "description": "社团公开接口"}, {"name": "Artists", "description": "作者公开接口"}, {"name": "Appearances", "description": "参展记录公开接口"}, {"name": "Bookmarks", "description": "收藏功能接口"}, {"name": "Search", "description": "搜索功能接口"}, {"name": "Feed", "description": "Feed 流接口"}, {"name": "Images", "description": "图片公开接口"}, {"name": "Rich Text", "description": "富文本内容管理接口"}], "servers": [{"url": "https://api.example.com", "description": "Production server"}, {"url": "http://localhost:8787", "description": "Local dev server"}]}