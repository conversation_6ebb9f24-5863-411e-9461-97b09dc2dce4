import { Context, Next } from 'hono';
import { BetterAuthContext } from '../types';

/**
 * Better Auth 中间件
 * 替换原有的 authMiddleware，保持兼容性
 */
export const betterAuthMiddleware = () => {
  return async (c: Context, next: Next) => {
    // 初始化认证上下文
    const authContext: BetterAuthContext = {
      user: null,
      session: null,
    };

    try {
      // 从 Cookie 或 Authorization header 获取 token
      let token: string | null = null;

      // 1. 尝试从 Cookie 获取
      const cookieHeader = c.req.header('Cookie');
      if (cookieHeader) {
        const match = cookieHeader.match(/better-auth\.session_token=([^;]+)/);
        if (match && match[1]) {
          // URL解码token，因为Cookie中的token可能被编码
          token = decodeURIComponent(match[1]);
        }
      }

      // 2. 尝试从 Authorization header 获取
      if (!token) {
        const authHeader = c.req.header('Authorization');
        if (authHeader && authHeader.startsWith('Bearer ')) {
          token = authHeader.substring(7);
        }
      }

      if (token) {
        console.log('[BetterAuth] Found token:', token.substring(0, 8) + '...');

        // 直接查询数据库验证 token（作为 Better Auth API 的备用方案）
        const db = c.env.DB;
        const result = await db
          .prepare(
            `
            SELECT s.id as session_id, s.user_id, s.expires_at,
                   u.id, u.email, u.name, u.username, u.role
            FROM session s
            JOIN user u ON s.user_id = u.id
            WHERE s.token = ? AND s.expires_at > ?
          `
          )
          .bind(token, Date.now()) // 使用毫秒级时间戳，与数据库存储格式一致
          .first();

        if (result) {
          console.log(
            '[BetterAuth] Session validated for user:',
            result.username || result.email
          );

          // 设置用户信息
          authContext.user = {
            id: result.id,
            email: result.email,
            name: result.name || result.username,
            role: result.role || 'user',
          };

          authContext.session = {
            id: result.session_id,
            userId: result.user_id,
            expiresAt: new Date(result.expires_at), // 数据库中已经是毫秒级时间戳
          };

          // 兼容现有的 roleGuard：设置用户信息到 c.var.user
          c.set('user', {
            id: authContext.user.id,
            role: authContext.user.role,
            username: authContext.user.name || authContext.user.email,
            email: authContext.user.email,
          });

          console.log('[BetterAuth] User context set:', {
            id: authContext.user.id,
            role: authContext.user.role,
            username: authContext.user.name,
          });
        } else {
          console.log('[BetterAuth] Invalid or expired token');
        }
      } else {
        console.log('[BetterAuth] No token found in request');
      }
    } catch (error) {
      // 认证失败时静默处理，不影响公开路由
      console.error('[BetterAuth] Error validating session:', error);
    }

    // 设置认证上下文，保持与原有 authMiddleware 的兼容性
    c.set('auth', authContext);

    await next();
  };
};

/**
 * 获取当前认证用户的辅助函数
 */
export const getCurrentUser = (c: Context) => {
  const authContext = c.get('auth') as BetterAuthContext;
  return authContext?.user || null;
};

/**
 * 获取当前会话的辅助函数
 */
export const getCurrentSession = (c: Context) => {
  const authContext = c.get('auth') as BetterAuthContext;
  return authContext?.session || null;
};

/**
 * 检查用户是否已认证的辅助函数
 */
export const isAuthenticated = (c: Context): boolean => {
  const user = getCurrentUser(c);
  return user !== null;
};

/**
 * 检查用户角色的辅助函数
 */
export const hasRole = (c: Context, role: string | string[]): boolean => {
  const user = getCurrentUser(c);
  if (!user) return false;
  const roles = Array.isArray(role) ? role : [role];
  return roles.includes(user.role);
};
